import axios from 'axios';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api';

const api = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
});

// API Response wrapper
export interface ApiResponse<T> {
  success: boolean;
  data: T;
  meta?: {
    total: number;
    provider: string;
  };
}

// Types
export interface City {
  id: string;
  name: string;
  iata_code: string;
  iata_country_code: string;
}

export interface Airport {
  id: string;
  name: string;
  city_name: string;
  city: City;
  iata_country_code: string;
  iata_code: string;
  icao_code: string;
  iata_city_code: string;
  latitude: string;
  longitude: string;
  time_zone: string;
  type: string;
  is_major: boolean;
}

export interface Airline {
  id: string;
  name: string;
  iata_code: string;
  icao_code: string;
}

export interface FlightSearchRequest {
  passengers: Array<{ type: 'adult' | 'child' | 'infant' }>;
  slices: Array<{
    origin: string;
    destination: string;
    departure_date: string;
  }>;
  cabin_class: 'economy' | 'premium_economy' | 'business' | 'first';
}

export interface FlightOffer {
  id: number;
  external_id: string;
  provider: string;
  status: string;
  pricing: {
    total_amount: string;
    total_currency: string;
    base_amount: string;
    base_currency: string;
    tax_amount: string;
    tax_currency: string;
  };
  airline: {
    code: string;
    name: string;
  };
  flight_details: {
    slices: Array<{
      id: string;
      origin: Airport;
      destination: Airport;
      duration: string;
      segments: Array<{
        id: string;
        origin: Airport;
        destination: Airport;
        departing_at: string;
        arriving_at: string;
        duration: string;
        marketing_carrier: {
          id: string;
          name: string;
          iata_code: string;
          logo_lockup_url?: string;
          logo_symbol_url?: string;
        };
        marketing_carrier_flight_number: string;
        operating_carrier: {
          id: string;
          name: string;
          iata_code: string;
          logo_lockup_url?: string;
          logo_symbol_url?: string;
        };
        operating_carrier_flight_number: string;
        aircraft?: {
          id: string;
          name: string;
          iata_code: string;
        };
      }>;
      total_duration_minutes: number;
      total_stops: number;
      is_direct: boolean;
    }>;
  };
  booking_info: {
    expires_at: string;
    live_mode: boolean;
  };
  created_at: string;
  updated_at: string;
}

// Filter options interface
export interface FilterOptions {
  airlines?: Array<{
    code: string;
    name: string;
    count?: number;
  }>;
  stops?: number[];
  cabin_classes?: string[];
  departure_times?: Array<{
    label: string;
    min_hour: number;
    max_hour: number;
  }>;
  arrival_times?: Array<{
    label: string;
    min_hour: number;
    max_hour: number;
  }>;
  aircraft?: string[];
  price_range?: {
    min: number;
    max: number;
    currency?: string;
  };
  duration_range?: {
    min: number;
    max: number;
  };
}

// Flight filters interface
export interface FlightFilters {
  airlines?: string[];
  stops?: number[];
  cabin_classes?: string[];
  departure_times?: string[];
  arrival_times?: string[];
  aircraft?: string[];
  price_range?: [number, number];
  duration_range?: [number, number];
  min_price?: number;
  max_price?: number;
  min_duration?: number;
  max_duration?: number;
  baggage_included?: string[];
  refundable?: boolean;
  changeable?: boolean;
  direct_flights_only?: boolean;
  sort_by?: string;
}

export interface FlightSearchResponse {
  filter_options: FilterOptions | null;
  id: number;
  provider: string;
  external_id: string;
  status: string;
  results: {
    total_offers: number;
    offers_available: number;
    offers_expired: number;
  };
  offers: FlightOffer[];
  searched_at: string;
  created_at: string;
  updated_at: string;
  search_criteria?: {
    passengers: Array<{ type: 'adult' | 'child' | 'infant' }>;
    slices: Array<{
      origin: string;
      destination: string;
      departure_date: string;
    }>;
    cabin_class: 'economy' | 'premium_economy' | 'business' | 'first';
  };
  search?: {
    search_criteria: {
      passengers: Array<{ type: 'adult' | 'child' | 'infant' }>;
      slices: Array<{
        origin: string;
        destination: string;
        departure_date: string;
      }>;
      cabin_class: 'economy' | 'premium_economy' | 'business' | 'first';
    };
    offers: FlightOffer[];
  };
}

// API Functions
export const airportsApi = {
  getAll: () => api.get<ApiResponse<Airport[]>>('/flights/airports'),
  search: (query: string) => api.get<ApiResponse<Airport[]>>(`/flights/airports?query=${query}`),
};

export const airlinesApi = {
  getAll: () => api.get<Airline[]>('/flights/airlines'),
};

export const flightsApi = {
  search: (searchData: FlightSearchRequest) =>
    api.post<ApiResponse<FlightSearchResponse>>('/flights/search', searchData),

  getSearchResults: (searchId: number) =>
    api.get<ApiResponse<FlightSearchResponse>>(`/flights/searches/${searchId}`),

  getSearchOffers: (searchId: number, sort?: string, direction?: 'asc' | 'desc') => {
    const params = new URLSearchParams();
    if (sort) params.append('sort', sort);
    if (direction) params.append('direction', direction);
    return api.get<ApiResponse<FlightOffer[]>>(`/flights/searches/${searchId}/offers?${params}`);
  },

  getOfferDetails: (offerId: number) =>
    api.get<ApiResponse<FlightOffer>>(`/flights/offers/${offerId}`),
};

export const bookingsApi = {
  book: (bookingData: {
    offer_id: number;
    passengers: Array<{
      given_name: string;
      family_name: string;
    }>;
    payments: Array<{
      type: string;
      amount: string;
      currency: string;
    }>;
  }) => api.post('/flights/book', bookingData),
  
  getBooking: (bookingId: number) => api.get(`/flights/bookings/${bookingId}`),
  
  getAllBookings: () => api.get('/flights/bookings'),
  
  cancelBooking: (bookingId: number) => api.post(`/flights/bookings/${bookingId}/cancel`),
};
