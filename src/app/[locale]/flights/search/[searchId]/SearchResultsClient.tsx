'use client';

import { useTranslations, useLocale } from 'next-intl';
import { useRouter } from 'next/navigation';
import { useEffect, useState } from 'react';
import Header from '@/components/Header';
import FlightResults from '@/components/FlightResults';

import FlightSearchSummaryBox from '@/components/FlightSearchSummaryBox';
import FlightFilters from '@/components/FlightFilters';
import { FlightOffer, flightsApi, FlightSearchRequest, FilterOptions, FlightFilters as FlightFiltersType } from '@/lib/api';

interface Props {
  searchId: number;
}

export default function SearchResultsClient({ searchId }: Props) {
  const t = useTranslations();
  const locale = useLocale();
  const router = useRouter();
  const [searchData, setSearchData] = useState<FlightSearchRequest | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [currentSearchId, setCurrentSearchId] = useState<number>(searchId);
  const [filterOptions, setFilterOptions] = useState<FilterOptions | null>(null);
  const [offers, setOffers] = useState<FlightOffer[]>([]);
  const [filters, setFilters] = useState<FlightFiltersType>({});

  useEffect(() => {
    // گرفتن داده جستجو برای مقداردهی اولیه فرم
    const fetchSearch = async () => {
      setLoading(true);
      setError(null);
      try {
        const res = await flightsApi.getSearchResults(currentSearchId);
        console.log('Search results response:', res.data);

        if (res.data.success) {
          // Check different possible response structures
          const responseData = res.data.data;
          const searchCriteria = responseData.search_criteria || responseData.search?.search_criteria;

          if (searchCriteria) {
            setSearchData({
              passengers: searchCriteria.passengers || [{ type: 'adult' }],
              slices: (searchCriteria.slices || []).map((s: { origin: string; destination: string; departure_date: string }) => ({
                origin: s.origin,
                destination: s.destination,
                departure_date: s.departure_date,
              })),
              cabin_class: searchCriteria.cabin_class || 'economy',
            });
          } else {
            // Fallback to default values if search_criteria is not found
            console.warn('search_criteria not found in response, using defaults');
            setSearchData({
              passengers: [{ type: 'adult' }],
              slices: [{ origin: '', destination: '', departure_date: '' }],
              cabin_class: 'economy',
            });
          }

          setFilterOptions(responseData.filter_options || null);
          setOffers(responseData.offers || responseData.search?.offers || []);
        } else {
          console.error('API returned success: false', res.data);
          setError('Failed to load search results');
        }
      } catch (err) {
        console.error('Error fetching search results:', err);
        setError('Failed to load search results');
      } finally {
        setLoading(false);
      }
    };
    fetchSearch();
  }, [currentSearchId]);

  // اعمال فیلترها
  const applyFilters = async (newFilters: FlightFiltersType) => {
    setFilters(newFilters);
    setLoading(true);
    try {
      const res = await fetch(`/api/flights/searches/${currentSearchId}/offers/filter`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newFilters),
      });
      const data = await res.json();
      if (data.success) {
        setOffers(data.data.offers || []);
        setFilterOptions(data.data.filter_options || filterOptions);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleSelectFlight = (offer: FlightOffer) => {
    // Navigate to booking page
    router.push(`/${locale}/flights/book/${offer.id}`);
  };

  const handleEditSearch = async (newSearch: FlightSearchRequest) => {
    setLoading(true);
    try {
      const res = await flightsApi.search(newSearch);
      if (res.data.success) {
        setCurrentSearchId(res.data.data.id);
      }
    } finally {
      setLoading(false);
    }
  };

  // Helper to map FlightSearchRequest to summary box props
  function getSummaryBoxData(searchData: FlightSearchRequest) {
    const adults = searchData.passengers.filter(p => p.type === 'adult').length;
    const children = searchData.passengers.filter(p => p.type === 'child').length;
    const infants = searchData.passengers.filter(p => p.type === 'infant').length;
    const origin = searchData.slices[0]?.origin || '';
    const destination = searchData.slices[0]?.destination || '';
    const departureDate = searchData.slices[0]?.departure_date || '';
    const returnDate = searchData.slices[1]?.departure_date || undefined;
    return {
      origin,
      destination,
      departureDate,
      returnDate,
      adults,
      children,
      infants,
      cabinClass: searchData.cabin_class,
    };
  }

  // Handle editing a single field
  const handleEditSummaryField = (field: string, value: string | number | Date) => {
    if (!searchData) return;
    const newSearch: FlightSearchRequest = JSON.parse(JSON.stringify(searchData));
    switch (field) {
      case 'origin':
        newSearch.slices[0].origin = value as string;
        break;
      case 'destination':
        newSearch.slices[0].destination = value as string;
        break;
      case 'departureDate':
        newSearch.slices[0].departure_date = value instanceof Date ? value.toISOString().split('T')[0] : value as string;
        break;
      case 'returnDate':
        if (newSearch.slices[1]) {
          newSearch.slices[1].departure_date = value instanceof Date ? value.toISOString().split('T')[0] : value as string;
        } else if (value) {
          // Add return slice if not present
          newSearch.slices[1] = {
            origin: newSearch.slices[0].destination,
            destination: newSearch.slices[0].origin,
            departure_date: value instanceof Date ? value.toISOString().split('T')[0] : value as string,
          };
        }
        break;
      case 'adults':
      case 'children':
      case 'infants': {
        const count = parseInt(String(value), 10) || 0;
        newSearch.passengers = [
          ...Array(field === 'adults' ? count : newSearch.passengers.filter(p => p.type === 'adult').length).fill({ type: 'adult' }),
          ...Array(field === 'children' ? count : newSearch.passengers.filter(p => p.type === 'child').length).fill({ type: 'child' }),
          ...Array(field === 'infants' ? count : newSearch.passengers.filter(p => p.type === 'infant').length).fill({ type: 'infant' }),
        ];
        break;
      }
      case 'cabinClass':
        newSearch.cabin_class = value as 'economy' | 'premium_economy' | 'business' | 'first';
        break;
      default:
        break;
    }
    handleEditSearch(newSearch);
  };

  if (error) {
    return (
      <div className="min-h-screen bg-gray-50">
        <Header />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <div className="text-center">
            <h1 className="text-2xl font-bold text-gray-900 mb-4">Error Loading Search Results</h1>
            <p className="text-gray-600 mb-4">{error}</p>
            <button
              onClick={() => router.back()}
              className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700"
            >
              Go Back
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <Header />
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-row">
          <FlightFilters filterOptions={filterOptions} filters={filters} onChange={applyFilters} />
          <main className="flex-1 min-w-0">
            <div className="mb-8">
              <h1 className="text-3xl font-bold text-gray-900 mb-2">
                {t('results.title')}
              </h1>
              <button
                onClick={() => router.back()}
                className="text-blue-600 hover:text-blue-800 text-sm font-medium"
              >
                ← {t('common.previous')}
              </button>
            </div>
            {/* فرم جستجو برای ویرایش */}
            {searchData && (
              <div className="max-w-4xl mx-auto mb-10">
                <FlightSearchSummaryBox
                  searchData={getSummaryBoxData(searchData)}
                  onEdit={handleEditSummaryField}
                  loading={loading}
                />
              </div>
            )}
            <FlightResults 
              offers={offers}
              onSelectFlight={handleSelectFlight}
              passengerCount={searchData ? searchData.passengers.length : 1}
              loading={loading}
            />
          </main>
        </div>
      </div>
    </div>
  );
}
