'use client';

import { useState, useEffect } from 'react';
import { useTranslations } from 'next-intl';
import { useForm } from 'react-hook-form';
import { Search, Calendar, Users, ArrowUpDown, Plane, Hotel, Mountain, Plus, X as Close } from 'lucide-react';
import DatePicker from 'react-datepicker';
import "react-datepicker/dist/react-datepicker.css";
import { Airport, FlightSearchRequest } from '@/lib/api';
import { cn } from '@/lib/utils';
import AirportSearch from './AirportSearch';
import Counter from './Counter';
import toast, { Toaster } from 'react-hot-toast';

interface FlightSearchFormData {
  originInput: string;
  destinationInput: string;
  originCode: string;
  destinationCode: string;
  departureDate: Date;
  returnDate?: Date;
  adults: number;
  children: number;
  infants: number;
  cabinClass: 'economy' | 'premium_economy' | 'business' | 'first';
  tripType: 'oneWay' | 'roundTrip' | 'multiCity';
}

interface Props {
  onSearch: (searchData: FlightSearchRequest) => void;
  loading?: boolean;
  initialValues?: Partial<FlightSearchRequest>;
  inline?: boolean;
}

export default function FlightSearchForm({ onSearch, loading = false, initialValues, inline = false }: Props) {
  const t = useTranslations();
  const [selectedOrigin, setSelectedOrigin] = useState<Airport | null>(null);
  const [selectedDestination, setSelectedDestination] = useState<Airport | null>(null);

  // Set default departure date to tomorrow
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);

  // مقداردهی اولیه فرم با initialValues
  const initialSlice = initialValues?.slices?.[0];
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors }
  } = useForm<FlightSearchFormData>({
    defaultValues: {
      originInput: initialSlice?.origin || '',
      destinationInput: initialSlice?.destination || '',
      originCode: initialSlice?.origin || '',
      destinationCode: initialSlice?.destination || '',
      adults: initialValues?.passengers?.filter(p => p.type === 'adult').length || 1,
      children: initialValues?.passengers?.filter(p => p.type === 'child').length || 0,
      infants: initialValues?.passengers?.filter(p => p.type === 'infant').length || 0,
      cabinClass: initialValues?.cabin_class || 'economy',
      tripType: (initialValues?.slices && initialValues.slices.length > 1) ? 'roundTrip' : 'oneWay',
      departureDate: initialSlice?.departure_date ? new Date(initialSlice.departure_date) : tomorrow,
      returnDate: initialValues?.slices && initialValues.slices[1]?.departure_date ? new Date(initialValues.slices[1].departure_date) : undefined,
    }
  });

  const tripType = watch('tripType');
  const departureDate = watch('departureDate');
  const returnDate = watch('returnDate');
  const originInput = watch('originInput');
  const destinationInput = watch('destinationInput');

  // برای انیمیشن چرخش دکمه swap
  const [swapRotated, setSwapRotated] = useState(false);

  const handleOriginSelect = (airport: Airport) => {
    setSelectedOrigin(airport);
    setValue('originCode', airport.iata_code);
  };

  const handleDestinationSelect = (airport: Airport) => {
    setSelectedDestination(airport);
    setValue('destinationCode', airport.iata_code);
  };

  const swapAirports = () => {
    setSwapRotated(true);
    setTimeout(() => setSwapRotated(false), 400);
    const tempOrigin = selectedOrigin;
    const tempDestination = selectedDestination;
    const tempOriginInput = originInput;
    const tempDestinationInput = destinationInput;

    setSelectedOrigin(tempDestination);
    setSelectedDestination(tempOrigin);
    setValue('originInput', tempDestinationInput);
    setValue('destinationInput', tempOriginInput);
    setValue('originCode', tempDestination?.iata_code || '');
    setValue('destinationCode', tempOrigin?.iata_code || '');
  };

  const resetForm = () => {
    setSelectedOrigin(null);
    setSelectedDestination(null);
    setValue('originInput', '');
    setValue('destinationInput', '');
    setValue('originCode', '');
    setValue('destinationCode', '');
    setValue('adults', 1);
    setValue('children', 0);
    setValue('infants', 0);
    setValue('cabinClass', 'economy');
    setValue('tripType', 'roundTrip');
    setValue('departureDate', tomorrow);
    setValue('returnDate', undefined);
  };

  const [multiCitySegments, setMultiCitySegments] = useState([
    { originInput: '', destinationInput: '', originCode: '', destinationCode: '', date: tomorrow }
  ]);

  const handleMultiCityChange = (idx: number, field: string, value: any) => {
    setMultiCitySegments(segments => segments.map((seg, i) =>
      i === idx ? { ...seg, [field]: value } : seg
    ));
  };
  const handleMultiCityOriginSelect = (idx: number, airport: Airport) => {
    setMultiCitySegments(segments => segments.map((seg, i) =>
      i === idx ? { ...seg, originCode: airport.iata_code } : seg
    ));
  };
  const handleMultiCityDestinationSelect = (idx: number, airport: Airport) => {
    setMultiCitySegments(segments => segments.map((seg, i) =>
      i === idx ? { ...seg, destinationCode: airport.iata_code } : seg
    ));
  };
  const addMultiCitySegment = () => {
    setMultiCitySegments(segments => [
      ...segments,
      { originInput: '', destinationInput: '', originCode: '', destinationCode: '', date: tomorrow }
    ]);
  };
  const removeMultiCitySegment = (idx: number) => {
    setMultiCitySegments(segments => segments.length > 2 ? segments.filter((_, i) => i !== idx) : segments);
  };

  const onSubmit = (data: FlightSearchFormData) => {
    if (tripType === 'multiCity') {
      // جمع‌آوری داده‌های multiCity
      const valid = multiCitySegments.every(seg => seg.originCode && seg.destinationCode && seg.date);
      if (!valid) {
        toast.error(t('errors.required'));
        return;
      }
      const searchRequest: FlightSearchRequest = {
        passengers: [
          ...Array(data.adults).fill({ type: 'adult' }),
          ...Array(data.children).fill({ type: 'child' }),
          ...Array(data.infants).fill({ type: 'infant' }),
        ],
        slices: multiCitySegments.map(seg => ({
          origin: seg.originCode,
          destination: seg.destinationCode,
          departure_date: seg.date.toISOString().split('T')[0],
        })),
        cabin_class: data.cabinClass,
      };
      onSearch(searchRequest);
      return;
    }
    if (!data.originCode || !data.destinationCode) {
      toast.error(t('errors.required'));
      return;
    }

    const searchRequest: FlightSearchRequest = {
      passengers: [
        ...Array(data.adults).fill({ type: 'adult' }),
        ...Array(data.children).fill({ type: 'child' }),
        ...Array(data.infants).fill({ type: 'infant' }),
      ],
      slices: [
        {
          origin: data.originCode,
          destination: data.destinationCode,
          departure_date: data.departureDate.toISOString().split('T')[0],
        },
        ...(data.tripType === 'roundTrip' && data.returnDate ? [{
          origin: data.destinationCode,
          destination: data.originCode,
          departure_date: data.returnDate.toISOString().split('T')[0],
        }] : [])
      ],
      cabin_class: data.cabinClass,
    };

    console.log('Search request:', searchRequest);
    onSearch(searchRequest);
  };

  // پاک‌سازی فیلدها هنگام تغییر tripType
  useEffect(() => {
    if (tripType === 'multiCity') {
      setValue('originInput', '');
      setValue('destinationInput', '');
      setValue('originCode', '');
      setValue('destinationCode', '');
      setValue('departureDate', tomorrow);
      setValue('returnDate', undefined);
    } else {
      setMultiCitySegments([
        { originInput: '', destinationInput: '', originCode: '', destinationCode: '', date: tomorrow }
      ]);
    }
  }, [tripType]);

  return (
    <div className={cn("bg-white rounded-2xl shadow-xl", inline ? "p-4" : "p-8") }>
      {/* Tabs for Flight, Hotel, Tour */}
      <div className="flex justify-center gap-2 md:gap-4 mb-8 md:mb-10">
        <button
          type="button"
          className="flex items-center gap-1 md:gap-2 px-4 py-2 md:px-8 md:py-3 rounded-full text-base md:text-lg font-bold border-2 border-blue-600 bg-white text-blue-600 shadow-lg cursor-default transition-all duration-200 scale-105"
          disabled
        >
          <Plane className="h-5 w-5 md:h-6 md:w-6" />
          {t('common.flight', { defaultValue: 'Flight' })}
        </button>
        <button
          type="button"
          className="flex items-center gap-1 md:gap-2 px-4 py-2 md:px-8 md:py-3 rounded-full text-base md:text-lg font-bold border-2 border-gray-200 bg-gray-50 text-gray-400 hover:bg-gray-100 hover:text-blue-500 transition-all duration-200 cursor-not-allowed"
          disabled
        >
          <Hotel className="h-5 w-5 md:h-6 md:w-6" />
          {t('common.hotel', { defaultValue: 'Hotel' })}
        </button>
        <button
          type="button"
          className="flex items-center gap-1 md:gap-2 px-4 py-2 md:px-8 md:py-3 rounded-full text-base md:text-lg font-bold border-2 border-gray-200 bg-gray-50 text-gray-400 hover:bg-gray-100 hover:text-blue-500 transition-all duration-200 cursor-not-allowed"
          disabled
        >
          <Mountain className="h-5 w-5 md:h-6 md:w-6" />
          {t('common.tour', { defaultValue: 'Tour' })}
        </button>
      </div>
      <Toaster position="top-center" toastOptions={{
        style: { borderRadius: '12px', background: '#fff', color: '#222', fontSize: '0.95rem', boxShadow: '0 2px 12px 0 #0001' },
        duration: 2500
      }} />
      <form onSubmit={handleSubmit(onSubmit)} className="space-y-8">
        {/* Trip Type */}
        <div className="flex flex-wrap gap-4 mb-2">
          <label className="flex items-center text-sm font-medium text-gray-600">
            <input
              type="radio"
              value="roundTrip"
              {...register('tripType')}
              className="mr-2 accent-blue-600"
            />
            {t('search.roundTrip')}
          </label>
          <label className="flex items-center text-sm font-medium text-gray-600">
            <input
              type="radio"
              value="oneWay"
              {...register('tripType')}
              className="mr-2 accent-blue-600"
            />
            {t('search.oneWay')}
          </label>
          <label className="flex items-center text-sm font-medium text-gray-600 opacity-80">
            <input
              type="radio"
              value="multiCity"
              {...register('tripType')}
              className="mr-2 accent-blue-600"
            />
            {t('search.multiCity', { defaultValue: 'Multi-city' })}
          </label>
        </div>

        {/* Origin and Destination (only for oneWay/roundTrip) */}
        {(tripType === 'oneWay' || tripType === 'roundTrip') && (
          inline ? (
            <div className="flex flex-row flex-wrap gap-2 md:gap-4 items-end w-full overflow-x-auto">
              <div className="min-w-[160px]">
                <AirportSearch
                  placeholder={t('search.fromPlaceholder')}
                  value={originInput}
                  onChange={(value) => setValue('originInput', value)}
                  onSelect={handleOriginSelect}
                />
                <input type="hidden" {...register('originCode', { required: true })} />
              </div>
              <button
                type="button"
                onClick={swapAirports}
                className={cn(
                  "bg-blue-50 text-blue-600 p-2 rounded-full shadow hover:bg-blue-100 transition-transform duration-300 mt-1",
                  swapRotated && "rotate-180"
                )}
                style={{ minWidth: 40 }}
              >
                <ArrowUpDown className="h-5 w-5" />
              </button>
              <div className="min-w-[160px]">
                <AirportSearch
                  placeholder={t('search.toPlaceholder')}
                  value={destinationInput}
                  onChange={(value) => setValue('destinationInput', value)}
                  onSelect={handleDestinationSelect}
                />
                <input type="hidden" {...register('destinationCode', { required: true })} />
              </div>
              <div className="min-w-[150px]">
                <DatePicker
                  selected={departureDate}
                  onChange={(date) => setValue('departureDate', date!)}
                  minDate={tomorrow}
                  dateFormat="dd/MM/yyyy"
                  placeholderText={t('search.departureDate')}
                  className="w-full pl-4 pr-4 py-2 border border-gray-200 rounded-full focus:ring-2 focus:ring-blue-200 focus:border-blue-400 text-gray-900 bg-white transition text-sm font-semibold"
                />
              </div>
              {tripType === 'roundTrip' && (
                <div className="min-w-[150px]">
                  <DatePicker
                    selected={returnDate}
                    onChange={(date) => setValue('returnDate', date!)}
                    minDate={departureDate}
                    dateFormat="dd/MM/yyyy"
                    placeholderText={t('search.returnDate')}
                    className="w-full pl-4 pr-4 py-2 border border-gray-200 rounded-full focus:ring-2 focus:ring-pink-200 focus:border-pink-400 text-gray-900 bg-white transition text-sm font-semibold"
                  />
                </div>
              )}
              <div className="min-w-[110px]">
                <select
                  {...register('cabinClass')}
                  className="w-full px-3 py-2 border border-gray-200 rounded-full focus:ring-blue-200 focus:border-blue-400 text-gray-900 bg-white text-sm font-semibold"
                >
                  <option value="economy">{t('common.economy')}</option>
                  <option value="premium_economy">{t('common.premium_economy')}</option>
                  <option value="business">{t('common.business')}</option>
                  <option value="first">{t('common.first')}</option>
                </select>
              </div>
              <div className="min-w-[110px]">
                <Counter
                  value={watch('adults')}
                  onChange={v => setValue('adults', v)}
                  min={1}
                  max={9}
                  label={t('common.adults')}
                />
              </div>
              <div className="min-w-[110px]">
                <Counter
                  value={watch('children')}
                  onChange={v => setValue('children', v)}
                  min={0}
                  max={9}
                  label={t('common.children')}
                />
              </div>
              <div className="min-w-[110px]">
                <Counter
                  value={watch('infants')}
                  onChange={v => setValue('infants', v)}
                  min={0}
                  max={9}
                  label={t('common.infants')}
                />
              </div>
              <button
                type="submit"
                disabled={loading}
                className={cn(
                  "bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-full font-bold shadow transition flex items-center gap-2 btn-glow",
                  loading && "opacity-50 cursor-not-allowed"
                )}
                style={{ minWidth: 120 }}
              >
                <Search className="h-5 w-5" />
                <span>{loading ? t('common.loading') : t('search.searchFlights')}</span>
              </button>
            </div>
          ) : (
            // حالت قبلی (grid)
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 relative">
              {/* Origin */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center gap-1">
                  <Plane className="h-4 w-4 text-blue-400" />
                  {t('common.from')}
                </label>
                <AirportSearch
                  placeholder={t('search.fromPlaceholder')}
                  value={originInput}
                  onChange={(value) => setValue('originInput', value)}
                  onSelect={handleOriginSelect}
                />
                <input type="hidden" {...register('originCode', { required: true })} />
                {errors.originCode && (
                  <p className="mt-1 text-sm text-red-600">{t('errors.required')}</p>
                )}
              </div>
              {/* Swap Button */}
              <div className="absolute left-1/2 top-8 transform -translate-x-1/2 z-10 hidden md:block">
                <button
                  type="button"
                  onClick={swapAirports}
                  className={cn(
                    "bg-blue-50 text-blue-600 p-2 rounded-full shadow hover:bg-blue-100 transition-transform duration-300",
                    swapRotated && "rotate-180"
                  )}
                >
                  <ArrowUpDown className="h-5 w-5" />
                </button>
              </div>
              {/* Destination */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center gap-1">
                  <Plane className="h-4 w-4 text-pink-400 rotate-90" />
                  {t('common.to')}
                </label>
                <AirportSearch
                  placeholder={t('search.toPlaceholder')}
                  value={destinationInput}
                  onChange={(value) => setValue('destinationInput', value)}
                  onSelect={handleDestinationSelect}
                />
                <input type="hidden" {...register('destinationCode', { required: true })} />
                {errors.destinationCode && (
                  <p className="mt-1 text-sm text-red-600">{t('errors.required')}</p>
                )}
              </div>
            </div>
          )
        )}
        {/* Dates */}
        {tripType !== 'multiCity' ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center gap-1">
                <Calendar className="h-4 w-4 text-blue-400" />
                {t('search.departureDate')}
              </label>
              <div className="relative">
                <DatePicker
                  selected={departureDate}
                  onChange={(date) => setValue('departureDate', date!)}
                  minDate={tomorrow}
                  dateFormat="dd/MM/yyyy"
                  placeholderText={t('search.departureDate')}
                  className="w-full pl-4 pr-4 py-2 border border-gray-200 rounded-full focus:ring-2 focus:ring-blue-200 focus:border-blue-400 text-gray-900 bg-white transition"
                />
              </div>
            </div>
            {tripType === 'roundTrip' && (
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1 flex items-center gap-1">
                  <Calendar className="h-4 w-4 text-pink-400" />
                  {t('search.returnDate')}
                </label>
                <div className="relative">
                  <DatePicker
                    selected={returnDate}
                    onChange={(date) => setValue('returnDate', date!)}
                    minDate={departureDate}
                    dateFormat="dd/MM/yyyy"
                    placeholderText={t('search.returnDate')}
                    className="w-full pl-4 pr-4 py-2 border border-gray-200 rounded-full focus:ring-2 focus:ring-pink-200 focus:border-pink-400 text-gray-900 bg-white transition"
                  />
                </div>
              </div>
            )}
          </div>
        ) : (
          <div className="space-y-5 mb-4">
            {multiCitySegments.map((seg, idx) => (
              <div key={idx} className="flex flex-col md:flex-row gap-2 md:gap-4 items-center bg-white border border-blue-100 shadow-md rounded-2xl px-3 py-4 relative animate-fadeInUp">
                <div className="flex-1 w-full">
                  <AirportSearch
                    placeholder={t('search.fromPlaceholder')}
                    value={seg.originInput}
                    onChange={val => handleMultiCityChange(idx, 'originInput', val)}
                    onSelect={airport => handleMultiCityOriginSelect(idx, airport)}
                  />
                </div>
                <div className="flex-1 w-full">
                  <AirportSearch
                    placeholder={t('search.toPlaceholder')}
                    value={seg.destinationInput}
                    onChange={val => handleMultiCityChange(idx, 'destinationInput', val)}
                    onSelect={airport => handleMultiCityDestinationSelect(idx, airport)}
                  />
                </div>
                <div className="w-full md:w-48 relative">
                  <Calendar className="absolute left-3 top-3 h-5 w-5 text-blue-400 pointer-events-none" />
                  <DatePicker
                    selected={seg.date}
                    onChange={date => handleMultiCityChange(idx, 'date', date)}
                    minDate={tomorrow}
                    dateFormat="dd/MM/yyyy"
                    placeholderText={t('search.departureDate')}
                    className="w-full pl-10 pr-4 py-2 border border-blue-200 rounded-full focus:ring-2 focus:ring-blue-200 focus:border-blue-400 text-gray-900 bg-white transition font-semibold"
                  />
                </div>
                {multiCitySegments.length > 2 && (
                  <button type="button" onClick={() => removeMultiCitySegment(idx)} className="ml-2 flex items-center justify-center w-9 h-9 rounded-full bg-red-50 hover:bg-red-100 text-red-500 hover:text-red-700 shadow transition absolute top-2 right-2 md:static md:top-auto md:right-auto">
                    <Close className="h-5 w-5" />
                  </button>
                )}
              </div>
            ))}
            <button type="button" onClick={addMultiCitySegment} className="flex items-center gap-2 mt-2 px-5 py-2 rounded-full bg-blue-600 text-white font-bold shadow hover:bg-blue-700 transition mx-auto">
              <Plus className="h-5 w-5" />
              {t('search.addSegment', { defaultValue: 'Add segment' })}
            </button>
          </div>
        )}
        {/* Passengers and Class */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('common.passengers')}
            </label>
            <div className="grid grid-cols-3 gap-4">
              <Counter
                value={watch('adults')}
                onChange={v => setValue('adults', v)}
                min={1}
                max={9}
                label={t('common.adults')}
              />
              <Counter
                value={watch('children')}
                onChange={v => setValue('children', v)}
                min={0}
                max={9}
                label={t('common.children')}
              />
              <Counter
                value={watch('infants')}
                onChange={v => setValue('infants', v)}
                min={0}
                max={9}
                label={t('common.infants')}
              />
            </div>
          </div>
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              {t('common.class')}
            </label>
            <div className="flex gap-2 mt-2">
              {['economy', 'premium_economy', 'business', 'first'].map(cls => (
                <button
                  key={cls}
                  type="button"
                  className={cn(
                    "px-4 py-2 rounded-full border text-sm font-medium transition",
                    watch('cabinClass') === cls
                      ? "bg-blue-600 text-white border-blue-600 shadow"
                      : "bg-white text-gray-700 border-gray-200 hover:bg-blue-50"
                  )}
                  onClick={() => setValue('cabinClass', cls as any)}
                >
                  {t(`common.${cls}`)}
                </button>
              ))}
            </div>
          </div>
        </div>
        {/* Search & Reset Buttons */}
        <div className="flex gap-3 mt-6">
          <button
            type="submit"
            disabled={loading}
            className={cn(
              "w-full bg-gradient-to-r from-blue-500 to-blue-700 text-white py-3 rounded-full font-bold shadow-lg hover:from-blue-600 hover:to-blue-800 transition flex items-center justify-center relative overflow-hidden btn-glow",
              loading && "opacity-50 cursor-not-allowed"
            )}
          >
            {loading ? (
              <svg className="animate-spin h-5 w-5 mr-2 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"><circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle><path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8v8z"></path></svg>
            ) : (
              <Search className="h-5 w-5 mr-2" />
            )}
            <span>{loading ? t('common.loading') : t('search.searchFlights')}</span>
            <span className="absolute left-0 top-0 w-full h-full bg-white opacity-0 hover:opacity-5 transition-opacity duration-300 pointer-events-none" />
          </button>
          <button
            type="button"
            onClick={resetForm}
            className="bg-gray-100 text-gray-700 py-3 px-6 rounded-full font-bold shadow hover:bg-gray-200 transition"
          >
            {t('common.reset')}
          </button>
        </div>
      </form>
    </div>
  );
}
