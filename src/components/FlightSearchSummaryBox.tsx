import { Calendar, Users, Plane, Edit2 } from 'lucide-react';
import { useState } from 'react';
import DatePicker from 'react-datepicker';
import { useTranslations } from 'next-intl';
import AirportSearch from './AirportSearch';
import { Airport } from '@/lib/api';
import { cn } from '@/lib/utils';

interface Props {
  searchData: {
    origin: string;
    destination: string;
    departureDate: string;
    returnDate?: string;
    adults: number;
    children: number;
    infants: number;
    cabinClass: string;
  };
  onEdit: (field: string, value: string | number | Date) => void;
  loading?: boolean;
}

export default function FlightSearchSummaryBox({ searchData, onEdit, loading }: Props) {
  const t = useTranslations();
  const [editField, setEditField] = useState<string | null>(null);
  const [tempValue, setTempValue] = useState<string | number | Date | null>(null);
  const [tempAirport, setTempAirport] = useState<Airport | null>(null);

  // Helper for showing value or input
  const renderField = (field: string, value: string | number, icon: React.ReactNode, inputType: 'text' | 'date' | 'select' = 'text', options?: Array<{value: string; label: string}>, label?: string) => {
    const isEditing = editField === field;
    return (
      <span className={cn('relative', isEditing && 'z-10')}> 
        {isEditing ? (
          <span className="inline-flex items-center gap-2 bg-white border border-blue-100 rounded-full px-3 py-1 shadow-sm animate-fade-in">
            {field === 'origin' || field === 'destination' ? (
              <AirportSearch
                placeholder={field === 'origin' ? t('search.fromPlaceholder') : t('search.toPlaceholder')}
                value={tempValue}
                onChange={setTempValue}
                onSelect={(airport: Airport) => { setTempValue(`${airport.city_name} (${airport.iata_code})`); setTempAirport(airport); }}
                className="min-w-[140px]"
              />
            ) : inputType === 'date' ? (
              <DatePicker
                selected={tempValue}
                onChange={date => setTempValue(date)}
                dateFormat="dd/MM/yyyy"
                className="px-3 py-1 rounded-full border border-blue-100 focus:ring-2 focus:ring-blue-100 focus:border-blue-300 text-gray-900 bg-white text-sm"
              />
            ) : inputType === 'select' && options ? (
              <select
                value={tempValue}
                onChange={e => setTempValue(e.target.value)}
                className="px-3 py-1 rounded-full border border-blue-100 focus:ring-2 focus:ring-blue-100 focus:border-blue-300 text-gray-900 bg-white text-sm"
              >
                {options.map(opt => <option key={opt.value} value={opt.value}>{opt.label}</option>)}
              </select>
            ) : (
              <input
                type="text"
                value={tempValue}
                onChange={e => setTempValue(e.target.value)}
                className="px-3 py-1 rounded-full border border-blue-100 focus:ring-2 focus:ring-blue-100 focus:border-blue-300 text-gray-900 bg-white text-sm"
              />
            )}
            <button
              className="bg-blue-600 text-white px-3 py-1 rounded-full text-xs font-semibold hover:bg-blue-700 transition"
              onClick={() => { setEditField(null); if (field === 'origin' || field === 'destination') { if (tempAirport) onEdit(field, tempAirport.iata_code); else onEdit(field, tempValue); setTempAirport(null); } else { onEdit(field, tempValue); } }}
              disabled={loading}
            >{t('common.save', { defaultValue: 'Save' })}</button>
            <button
              className="bg-gray-100 text-gray-600 px-2 py-1 rounded-full text-xs font-semibold hover:bg-gray-200 transition"
              onClick={() => { setEditField(null); setTempAirport(null); }}
              disabled={loading}
            >{t('common.cancel', { defaultValue: 'Cancel' })}</button>
          </span>
        ) : (
          <span
            className={cn(
              'inline-flex items-center gap-1 px-4 py-1 rounded-full text-sm font-medium mr-2 bg-blue-50 text-blue-900 border border-blue-100 shadow-sm',
              'transition-colors duration-200',
            )}
            onClick={() => { setEditField(field); setTempValue(value); }}
            style={{ cursor: 'pointer' }}
          >
            {icon}
            <span className="font-semibold">{field === 'origin' || field === 'destination' ? value.split('(')[0].trim() : value}</span>
            {field === 'origin' || field === 'destination' ? (
              <span className="ml-1 text-xs text-blue-400 font-mono">{value.match(/\(([^)]+)\)/)?.[1]}</span>
            ) : null}
            <button
              className="ml-1 text-blue-400 hover:text-blue-600"
              tabIndex={-1}
              style={{ background: 'none', border: 'none', padding: 0 }}
            >
              <Edit2 className="h-4 w-4" />
            </button>
          </span>
        )}
      </span>
    );
  };

  return (
    <div className="flex flex-wrap gap-3 items-center bg-white rounded-xl shadow-sm p-5 mb-8 border border-blue-100 rtl:flex-row-reverse">
      <div className="flex items-center gap-2 mr-4 rtl:ml-4 rtl:mr-0">
        {renderField('origin', searchData.origin, <Plane className="h-4 w-4 text-blue-400" />, 'text', undefined, t('common.from'))}
        <span className="mx-2 text-gray-300 text-lg font-light">–</span>
        {renderField('destination', searchData.destination, <Plane className="h-4 w-4 text-pink-400 rotate-90" />, 'text', undefined, t('common.to'))}
      </div>
      {renderField('departureDate', searchData.departureDate, <Calendar className="h-4 w-4 text-blue-400" />, 'date', undefined, t('search.departureDate'))}
      {searchData.returnDate && renderField('returnDate', searchData.returnDate, <Calendar className="h-4 w-4 text-pink-400" />, 'date', undefined, t('search.returnDate'))}
      {renderField('adults', searchData.adults, <Users className="h-4 w-4 text-blue-400" />, 'text', undefined, t('common.adults'))}
      {renderField('children', searchData.children, <Users className="h-4 w-4 text-yellow-400" />, 'text', undefined, t('common.children'))}
      {renderField('infants', searchData.infants, <Users className="h-4 w-4 text-purple-400" />, 'text', undefined, t('common.infants'))}
      {renderField('cabinClass', searchData.cabinClass, null, 'select', [
        { value: 'economy', label: t('common.economy') },
        { value: 'premium_economy', label: t('common.premium_economy') },
        { value: 'business', label: t('common.business') },
        { value: 'first', label: t('common.first') },
      ], t('common.cabinClass'))}
    </div>
  );
} 