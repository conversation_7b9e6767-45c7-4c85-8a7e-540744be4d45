import { useState } from 'react';
import { useTranslations } from 'next-intl';
import { cn } from '@/lib/utils';
import { FilterOptions, FlightFilters as FlightFiltersType } from '@/lib/api';

interface Props {
  filterOptions?: FilterOptions | null;
  filters?: FlightFiltersType;
  onChange?: (filters: FlightFiltersType) => void;
}

export default function FlightFilters({ filterOptions, filters = {}, onChange }: Props) {
  const t = useTranslations();
  const [localFilters, setLocalFilters] = useState<FlightFiltersType>(filters);

  // Update local state and notify parent
  const updateFilter = (key: keyof FlightFiltersType, value: FlightFiltersType[keyof FlightFiltersType]) => {
    const updated = { ...localFilters, [key]: value };
    setLocalFilters(updated);
    onChange?.(updated);
  };
  const handleCheckbox = (key: keyof FlightFiltersType) => updateFilter(key, !localFilters[key] as FlightFiltersType[keyof FlightFiltersType]);
  // For airlines, make it single-select (only one airline at a time)
  const handleAirlineSelect = (code: string) => {
    if (localFilters.airlines?.[0] === code) {
      updateFilter('airlines', []); // Deselect if already selected
    } else {
      updateFilter('airlines', [code]); // Only one airline at a time
    }
  };
  // For other filters (multi-select)
  const handleArrayToggle = (key: keyof FlightFiltersType, val: string | number) => {
    const arr = (localFilters[key] as (string | number)[]) || [];
    const newArr = arr.includes(val) ? arr.filter((v) => v !== val) : [...arr, val];
    updateFilter(key, newArr as FlightFiltersType[keyof FlightFiltersType]);
  };
  const handleReset = () => {
    setLocalFilters({});
    onChange?.({});
  };

  if (!filterOptions) {
    return (
      <aside className="hidden lg:block w-64 pr-6 sticky top-8">
        <div className="bg-white rounded-xl shadow-sm border border-blue-100 p-6 mb-6 animate-pulse">
          <div className="h-6 bg-blue-100 rounded w-1/2 mb-4"></div>
          {[1,2,3,4].map(i => <div key={i} className="h-4 bg-gray-100 rounded w-full mb-3"></div>)}
        </div>
      </aside>
    );
  }

  return (
    <aside className="hidden lg:block w-64 pr-6 sticky top-8">
      <div className="bg-white rounded-xl shadow-sm border border-blue-100 p-6 mb-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-bold text-gray-900">{t('filters.title', { defaultValue: 'Filters' })}</h3>
          <button onClick={handleReset} className="text-xs text-blue-600 hover:underline font-semibold">{t('common.reset')}</button>
        </div>
        {/* Airlines */}
        {filterOptions.airlines && (
          <div className="mb-6">
            <h4 className="text-sm font-semibold text-gray-800 mb-2">{t('filters.airlines', { defaultValue: 'Airlines' })}</h4>
            <div className="space-y-2 max-h-40 overflow-y-auto pr-1">
              {filterOptions.airlines.map((a) => (
                <label key={a.code} className="flex items-center gap-2 cursor-pointer">
                  <input type="checkbox" checked={localFilters.airlines?.[0] === a.code} onChange={() => handleAirlineSelect(a.code)} className="accent-blue-600" />
                  <span className="text-gray-700 flex-1">{a.name} <span className="text-xs text-gray-400 font-mono">({a.code})</span></span>
                </label>
              ))}
            </div>
          </div>
        )}
        {/* Stops */}
        {filterOptions.stops && (
          <div className="mb-6">
            <h4 className="text-sm font-semibold text-gray-800 mb-2">{t('filters.stops', { defaultValue: 'Stops' })}</h4>
            <div className="flex flex-wrap gap-2">
              {filterOptions.stops.map((stop: number) => (
                <label key={stop} className="flex items-center gap-1 cursor-pointer bg-blue-50 px-3 py-1 rounded-full border border-blue-100 text-blue-700 text-xs font-medium">
                  <input type="checkbox" checked={localFilters.stops?.includes(stop)} onChange={() => handleArrayToggle('stops', stop)} className="accent-blue-600" />
                  {stop === 0 ? t('filters.direct', { defaultValue: 'Direct' }) : t('filters.stopsCount', { count: stop, defaultValue: `${stop} stops` })}
                </label>
              ))}
            </div>
          </div>
        )}
        {/* Cabin Class */}
        {filterOptions.cabin_classes && (
          <div className="mb-6">
            <h4 className="text-sm font-semibold text-gray-800 mb-2">{t('common.cabinClass')}</h4>
            <div className="flex flex-wrap gap-2">
              {filterOptions.cabin_classes.map((cls: string) => (
                <label key={cls} className={cn('px-3 py-1 rounded-full border text-xs font-medium cursor-pointer', localFilters.cabin_classes?.includes(cls) ? 'bg-blue-600 text-white border-blue-600' : 'bg-white text-blue-700 border-blue-100 hover:bg-blue-50') }>
                  <input type="checkbox" checked={localFilters.cabin_classes?.includes(cls)} onChange={() => handleArrayToggle('cabin_classes', cls)} className="accent-blue-600 mr-1" />
                  {t(`common.${cls}`)}
                </label>
              ))}
            </div>
          </div>
        )}
        {/* Price Range */}
        {filterOptions.price_range && (
          <div className="mb-6">
            <h4 className="text-sm font-semibold text-gray-800 mb-2">{t('filters.price', { defaultValue: 'Price' })}</h4>
            <div className="flex items-center gap-2">
              <input
                type="number"
                min={filterOptions.price_range.min}
                max={filterOptions.price_range.max}
                value={localFilters.min_price ?? filterOptions.price_range.min}
                onChange={e => updateFilter('min_price', Number(e.target.value))}
                className="w-20 px-2 py-1 border border-gray-200 rounded-full text-sm"
              />
              <span className="text-gray-500">-</span>
              <input
                type="number"
                min={filterOptions.price_range.min}
                max={filterOptions.price_range.max}
                value={localFilters.max_price ?? filterOptions.price_range.max}
                onChange={e => updateFilter('max_price', Number(e.target.value))}
                className="w-20 px-2 py-1 border border-gray-200 rounded-full text-sm"
              />
              <span className="text-xs text-gray-500">{filterOptions.price_range.currency || 'USD'}</span>
            </div>
          </div>
        )}
        {/* Duration Range */}
        {filterOptions.duration_range && (
          <div className="mb-6">
            <h4 className="text-sm font-semibold text-gray-800 mb-2">{t('filters.duration', { defaultValue: 'Duration' })}</h4>
            <div className="flex items-center gap-2">
              <input
                type="number"
                min={filterOptions.duration_range.min}
                max={filterOptions.duration_range.max}
                value={localFilters.min_duration ?? filterOptions.duration_range.min}
                onChange={e => updateFilter('min_duration', Number(e.target.value))}
                className="w-20 px-2 py-1 border border-gray-200 rounded-full text-sm"
              />
              <span className="text-gray-500">-</span>
              <input
                type="number"
                min={filterOptions.duration_range.min}
                max={filterOptions.duration_range.max}
                value={localFilters.max_duration ?? filterOptions.duration_range.max}
                onChange={e => updateFilter('max_duration', Number(e.target.value))}
                className="w-20 px-2 py-1 border border-gray-200 rounded-full text-sm"
              />
              <span className="text-xs text-gray-500">min</span>
            </div>
          </div>
        )}
        {/* Aircraft */}
        {filterOptions.aircraft && (
          <div className="mb-6">
            <h4 className="text-sm font-semibold text-gray-800 mb-2">{t('filters.aircraft', { defaultValue: 'Aircraft' })}</h4>
            <div className="flex flex-wrap gap-2">
              {filterOptions.aircraft.map((ac: string) => (
                <label key={ac} className="flex items-center gap-1 cursor-pointer bg-blue-50 px-3 py-1 rounded-full border border-blue-100 text-blue-700 text-xs font-medium">
                  <input type="checkbox" checked={localFilters.aircraft?.includes(ac)} onChange={() => handleArrayToggle('aircraft', ac)} className="accent-blue-600" />
                  {ac}
                </label>
              ))}
            </div>
          </div>
        )}
        {/* Baggage */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-gray-800 mb-2">{t('filters.baggage', { defaultValue: 'Baggage' })}</h4>
          <div className="flex flex-wrap gap-2">
            {['carry_on', 'checked'].map(type => (
              <label key={type} className="flex items-center gap-1 cursor-pointer bg-blue-50 px-3 py-1 rounded-full border border-blue-100 text-blue-700 text-xs font-medium">
                <input type="checkbox" checked={localFilters.baggage_included?.includes(type)} onChange={() => handleArrayToggle('baggage_included', type)} className="accent-blue-600" />
                {t(`filters.${type}`, { defaultValue: type.replace('_', ' ') })}
              </label>
            ))}
          </div>
        </div>
        {/* Refundable/Changeable */}
        <div className="mb-6">
          <h4 className="text-sm font-semibold text-gray-800 mb-2">{t('filters.ticketConditions', { defaultValue: 'Ticket Conditions' })}</h4>
          <div className="flex flex-wrap gap-2">
            <label className="flex items-center gap-1 cursor-pointer bg-blue-50 px-3 py-1 rounded-full border border-blue-100 text-blue-700 text-xs font-medium">
              <input type="checkbox" checked={!!localFilters.refundable} onChange={() => handleCheckbox('refundable')} className="accent-blue-600" />
              {t('filters.refundable', { defaultValue: 'Refundable' })}
            </label>
            <label className="flex items-center gap-1 cursor-pointer bg-blue-50 px-3 py-1 rounded-full border border-blue-100 text-blue-700 text-xs font-medium">
              <input type="checkbox" checked={!!localFilters.changeable} onChange={() => handleCheckbox('changeable')} className="accent-blue-600" />
              {t('filters.changeable', { defaultValue: 'Changeable' })}
            </label>
          </div>
        </div>
        {/* Direct flights only */}
        <div className="mb-6">
          <label className="flex items-center gap-2 cursor-pointer">
            <input type="checkbox" checked={!!localFilters.direct_flights_only} onChange={() => handleCheckbox('direct_flights_only')} className="accent-blue-600" />
            <span className="text-gray-700">{t('filters.directFlightsOnly', { defaultValue: 'Direct flights only' })}</span>
          </label>
        </div>
        {/* Sorting */}
        <div className="mb-2">
          <h4 className="text-sm font-semibold text-gray-800 mb-2">{t('filters.sortBy', { defaultValue: 'Sort by' })}</h4>
          <select
            value={localFilters.sort_by || 'price'}
            onChange={e => updateFilter('sort_by', e.target.value)}
            className="w-full px-3 py-2 border border-gray-200 rounded-full text-gray-900 bg-white text-sm font-semibold"
          >
            <option value="price">{t('results.price')}</option>
            <option value="duration">{t('filters.duration')}</option>
            <option value="departure_time">{t('filters.departureTime', { defaultValue: 'Departure time' })}</option>
            <option value="arrival_time">{t('filters.arrivalTime', { defaultValue: 'Arrival time' })}</option>
          </select>
        </div>
      </div>
    </aside>
  );
} 