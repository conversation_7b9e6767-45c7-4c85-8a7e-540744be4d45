'use client';

import { useState } from 'react';
import { useLocale } from 'next-intl';
import { useRouter, usePathname } from 'next/navigation';
import { Plane, Globe, Menu, X, User, Heart, MapPin, Sparkles } from 'lucide-react';
import Link from 'next/link';
export default function Header() {
  const locale = useLocale();
  const router = useRouter();
  const pathname = usePathname();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const switchLocale = (newLocale: string) => {
    const segments = pathname.split('/');
    segments[1] = newLocale;
    router.push(segments.join('/'));
  };

  return (
    <header className="bg-white shadow-lg border-b border-gray-100 sticky top-0 z-40 backdrop-blur-sm bg-white/95">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex items-center justify-between h-16">
          {/* Logo */}
          <Link href={`/${locale}`} className="flex items-center gap-3 group">
            <div className="relative">
              <div className="w-10 h-10 bg-gradient-to-br from-blue-600 via-purple-600 to-indigo-700 rounded-xl flex items-center justify-center shadow-lg group-hover:shadow-xl transition-all duration-300 group-hover:scale-105">
                <Plane className="h-5 w-5 text-white transform rotate-45 group-hover:rotate-90 transition-transform duration-300" />
              </div>
              <div className="absolute -top-1 -right-1 w-3 h-3 bg-gradient-to-r from-orange-400 to-pink-500 rounded-full animate-pulse"></div>
            </div>
            <div className="hidden sm:block">
              <h1 className="text-xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-indigo-700 bg-clip-text text-transparent">
                Kiyans Trip
              </h1>
              <p className="text-xs text-gray-500 -mt-1 flex items-center gap-1">
                <Sparkles className="h-3 w-3" />
                Discover Your Adventure
              </p>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-8">
            <Link
              href={`/${locale}`}
              className="flex items-center gap-2 text-gray-700 hover:text-blue-600 transition-colors font-medium group"
            >
              <Plane className="h-4 w-4 group-hover:rotate-12 transition-transform" />
              Flights
            </Link>
            <Link
              href={`/${locale}/hotels`}
              className="flex items-center gap-2 text-gray-700 hover:text-blue-600 transition-colors font-medium group"
            >
              <MapPin className="h-4 w-4 group-hover:scale-110 transition-transform" />
              Hotels
            </Link>
            <Link
              href={`/${locale}/trips`}
              className="flex items-center gap-2 text-gray-700 hover:text-blue-600 transition-colors font-medium group"
            >
              <Heart className="h-4 w-4 group-hover:scale-110 transition-transform" />
              My Trips
            </Link>
          </nav>

          {/* Right Side */}
          <div className="flex items-center gap-4">
            {/* Language Switcher */}
            <div className="hidden sm:flex items-center gap-2">
              <Globe className="h-4 w-4 text-gray-500" />
              <button
                className="text-sm text-gray-700 hover:text-blue-600 font-medium transition-colors"
                onClick={() => {
                  const newLocale = locale === 'en' ? 'de' : 'en';
                  switchLocale(newLocale);
                }}
              >
                {locale === 'en' ? 'EN' : 'DE'}
              </button>
            </div>

            {/* User Menu */}
            <button className="flex items-center gap-2 bg-gradient-to-r from-blue-50 to-indigo-50 hover:from-blue-100 hover:to-indigo-100 px-3 py-2 rounded-lg transition-all duration-200 border border-blue-100">
              <User className="h-4 w-4 text-blue-600" />
              <span className="hidden sm:inline text-sm font-medium text-blue-700">Account</span>
            </button>

            {/* Mobile Menu Button */}
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="md:hidden p-2 rounded-lg hover:bg-gray-100 transition-colors"
            >
              {isMenuOpen ? (
                <X className="h-5 w-5 text-gray-600" />
              ) : (
                <Menu className="h-5 w-5 text-gray-600" />
              )}
            </button>
          </div>
        </div>

        {/* Mobile Menu */}
        {isMenuOpen && (
          <div className="md:hidden border-t border-gray-100 py-4 bg-gradient-to-r from-blue-50/50 to-indigo-50/50">
            <nav className="space-y-3">
              <Link
                href={`/${locale}`}
                className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-white/80 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                <Plane className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-gray-700">Flights</span>
              </Link>
              <Link
                href={`/${locale}/hotels`}
                className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-white/80 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                <MapPin className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-gray-700">Hotels</span>
              </Link>
              <Link
                href={`/${locale}/trips`}
                className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-white/80 transition-colors"
                onClick={() => setIsMenuOpen(false)}
              >
                <Heart className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-gray-700">My Trips</span>
              </Link>

              {/* Mobile Language Switcher */}
              <button
                className="flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-white/80 transition-colors w-full text-left"
                onClick={() => {
                  const newLocale = locale === 'en' ? 'de' : 'en';
                  switchLocale(newLocale);
                  setIsMenuOpen(false);
                }}
              >
                <Globe className="h-4 w-4 text-blue-600" />
                <span className="font-medium text-gray-700">
                  {locale === 'en' ? 'Switch to Deutsch' : 'Switch to English'}
                </span>
              </button>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}
