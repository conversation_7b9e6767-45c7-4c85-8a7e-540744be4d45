'use client';

import { useState, useEffect } from 'react';
import { useTranslations, useLocale } from 'next-intl';
import { Clock, Plane, ArrowRight, Wifi, Coffee, Tv, Star, Shield, Timer, MapPin, Info, X, Award, Zap, CheckCircle } from 'lucide-react';
import { FlightOffer, flightsApi } from '@/lib/api';
import { formatCurrency, formatTime, cn } from '@/lib/utils';
import Loading from './Loading';
import ErrorMessage from './ErrorMessage';

interface Props {
  offers: any[];
  onSelectFlight: (offer: any) => void;
  passengerCount?: number;
  loading?: boolean;
}

export default function FlightResults({ offers, onSelectFlight, passengerCount = 1, loading }: Props) {
  const t = useTranslations();
  const locale = useLocale();
  const [sortBy, setSortBy] = useState<'total_amount' | 'duration' | 'departure'>('total_amount');
  const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');
  const [selectedOffer, setSelectedOffer] = useState<FlightOffer | null>(null);
  const [showModal, setShowModal] = useState(false);

  const handleSort = (newSortBy: typeof sortBy) => {
    if (sortBy === newSortBy) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
    } else {
      setSortBy(newSortBy);
      setSortDirection('asc');
    }
  };

  const formatDuration = (duration: string) => {
    // Assuming duration is in format "PT2H30M"
    const match = duration.match(/PT(\d+H)?(\d+M)?/);
    if (!match) return duration;
    
    const hours = match[1] ? match[1].replace('H', 'h ') : '';
    const minutes = match[2] ? match[2].replace('M', 'm') : '';
    return `${hours}${minutes}`;
  };

  const getStopsText = (segments: any[]) => {
    const stops = segments.length - 1;
    if (stops === 0) return t('results.nonstop');
    if (stops === 1) return t('results.oneStop');
    return `${stops} ${t('results.multipleStops')}`;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-6 animate-pulse">
          <div className="h-8 bg-blue-200 rounded-lg w-1/3 mb-2"></div>
          <div className="h-4 bg-blue-100 rounded w-1/2"></div>
        </div>
        {[1, 2, 3].map((i) => (
          <div key={i} className="bg-white rounded-2xl shadow-lg border border-gray-100 p-4 animate-pulse">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gray-200 rounded-lg"></div>
                <div>
                  <div className="h-4 bg-gray-200 rounded w-24 mb-1"></div>
                  <div className="h-3 bg-gray-100 rounded w-20"></div>
                </div>
              </div>
              <div className="flex items-center gap-4 flex-1 justify-center">
                <div className="text-center">
                  <div className="h-6 bg-gray-200 rounded w-12 mb-1"></div>
                  <div className="h-3 bg-gray-100 rounded w-8"></div>
                </div>
                <div className="h-3 bg-gray-200 rounded w-16"></div>
                <div className="text-center">
                  <div className="h-6 bg-gray-200 rounded w-12 mb-1"></div>
                  <div className="h-3 bg-gray-100 rounded w-8"></div>
                </div>
              </div>
              <div className="text-right">
                <div className="h-6 bg-gray-200 rounded w-20 mb-1"></div>
                <div className="h-3 bg-gray-100 rounded w-12 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-16"></div>
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (offers.length === 0) {
    return (
      <div className="text-center py-16">
        <div className="bg-gradient-to-br from-blue-50 to-indigo-100 rounded-2xl p-12 max-w-md mx-auto">
          <div className="text-6xl mb-6 flex justify-center">
            <Plane className="h-16 w-16 text-blue-600" />
          </div>
          <h3 className="text-2xl font-bold text-gray-900 mb-4">
            {t('search.noResults')}
          </h3>
          <p className="text-gray-600 mb-6">
            We couldn't find any flights matching your criteria. Try adjusting your search parameters.
          </p>
          <div className="space-y-2 text-sm text-gray-500">
            <div>• Try different dates</div>
            <div>• Check nearby airports</div>
            <div>• Consider flexible travel options</div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header & Sort Controls */}
      <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl shadow-sm border border-blue-100 p-6 mb-6">
        <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold text-gray-900 mb-2 flex items-center gap-2">
              <Plane className="h-6 w-6 text-blue-600" />
              {offers.length} {t('search.resultsFound')}
            </h2>
            <p className="text-gray-600">
              Best deals sorted by price • Updated just now
            </p>
          </div>

          <div className="flex flex-wrap gap-2">
            <span className="text-sm font-medium text-gray-700 self-center mr-2">{t('results.sortBy')}:</span>
            <button
              onClick={() => handleSort('total_amount')}
              className={cn(
                "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2",
                sortBy === 'total_amount'
                  ? "bg-blue-600 text-white shadow-lg shadow-blue-600/25"
                  : "bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600 border border-gray-200"
              )}
            >
              <Star className="h-4 w-4" />
              {t('results.price')}
              {sortBy === 'total_amount' && (
                <span className="text-xs">{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </button>
            <button
              onClick={() => handleSort('duration')}
              className={cn(
                "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2",
                sortBy === 'duration'
                  ? "bg-blue-600 text-white shadow-lg shadow-blue-600/25"
                  : "bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600 border border-gray-200"
              )}
            >
              <Clock className="h-4 w-4" />
              {t('results.duration')}
              {sortBy === 'duration' && (
                <span className="text-xs">{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </button>
            <button
              onClick={() => handleSort('departure')}
              className={cn(
                "px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 flex items-center gap-2",
                sortBy === 'departure'
                  ? "bg-blue-600 text-white shadow-lg shadow-blue-600/25"
                  : "bg-white text-gray-700 hover:bg-blue-50 hover:text-blue-600 border border-gray-200"
              )}
            >
              <Timer className="h-4 w-4" />
              {t('results.departure')}
              {sortBy === 'departure' && (
                <span className="text-xs">{sortDirection === 'asc' ? '↑' : '↓'}</span>
              )}
            </button>
          </div>
        </div>
      </div>

      {/* Flight Offers */}
      <div className="max-w-4xl mx-auto space-y-4">
        {offers.map((offer, index) => (
          <div
            key={offer.id}
            className="flight-card group bg-white rounded-xl shadow-md border border-gray-100 hover:shadow-lg hover:border-blue-200 transition-all duration-300 overflow-hidden opacity-0"
            style={{ animationDelay: `${index * 0.1}s` }}
          >
            {/* Best Deal Badge */}
            {index === 0 && (
              <div className="bg-gradient-to-r from-green-500 to-emerald-500 text-white text-center py-1 px-4 flex items-center justify-center gap-1">
                <Award className="h-3 w-3" />
                <span className="text-xs font-semibold">Best Deal</span>
              </div>
            )}

            <div className="p-4">

              {offer.flight_details.slices.map((slice: any, sliceIndex: number) => (
                <div key={sliceIndex} className={cn("", sliceIndex > 0 && "mt-3 pt-3 border-t border-gray-100")}>
                  {/* Compact Flight Info */}
                  <div className="grid grid-cols-12 gap-4 items-center">
                    {/* Airline & Flight Info - 3 cols */}
                    <div className="col-span-3 flex items-center gap-2">
                      <div className="w-8 h-8 bg-gradient-to-br from-blue-500 to-blue-600 rounded-lg flex items-center justify-center text-white font-bold text-xs overflow-hidden flex-shrink-0">
                        {slice.segments[0]?.marketing_carrier.logo_symbol_url ? (
                          <img
                            src={slice.segments[0].marketing_carrier.logo_symbol_url}
                            alt={offer.airline.name}
                            className="w-full h-full object-contain"
                            onError={(e) => {
                              (e.currentTarget as HTMLImageElement).style.display = 'none';
                              const nextEl = e.currentTarget.nextElementSibling as HTMLElement;
                              if (nextEl) nextEl.style.display = 'flex';
                            }}
                          />
                        ) : null}
                        <div className={`w-full h-full flex items-center justify-center ${slice.segments[0]?.marketing_carrier.logo_symbol_url ? 'hidden' : 'flex'}`}>
                          {offer.airline.code}
                        </div>
                      </div>
                      <div className="min-w-0">
                        <div className="font-medium text-gray-900 text-sm truncate">{offer.airline.name}</div>
                        <div className="text-xs text-gray-500">
                          {slice.segments[0]?.marketing_carrier.iata_code}{slice.segments[0]?.marketing_carrier_flight_number}
                        </div>
                      </div>
                    </div>

                    {/* Departure - 2 cols */}
                    <div className="col-span-2 text-center">
                      <div className="text-lg font-bold text-gray-900">
                        {formatTime(new Date(slice.segments[0]?.departing_at))}
                      </div>
                      <div className="text-sm font-semibold text-blue-600">
                        {slice.origin.iata_code}
                      </div>
                    </div>

                    {/* Flight Path - 2 cols */}
                    <div className="col-span-2 flex flex-col items-center">
                      <div className="flex items-center w-full">
                        <div className="h-px bg-gray-300 flex-1"></div>
                        <Plane className="h-3 w-3 text-gray-400 mx-2 transform rotate-90" />
                        <div className="h-px bg-gray-300 flex-1"></div>
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        {formatDuration(slice.duration)}
                      </div>
                      <div className="text-xs">
                        {slice.is_direct ? (
                          <span className="text-green-600 flex items-center gap-1">
                            <CheckCircle className="h-3 w-3" />
                            Direct
                          </span>
                        ) : (
                          <span className="text-orange-600">{slice.total_stops} stop</span>
                        )}
                      </div>
                    </div>

                    {/* Arrival - 2 cols */}
                    <div className="col-span-2 text-center">
                      <div className="text-lg font-bold text-gray-900">
                        {formatTime(new Date(slice.segments[slice.segments.length - 1]?.arriving_at))}
                      </div>
                      <div className="text-sm font-semibold text-blue-600">
                        {slice.destination.iata_code}
                      </div>
                    </div>

                    {/* Price & Actions - 3 cols */}
                    <div className="col-span-3 text-right">
                      <div className="flex flex-col items-end mb-2">
                        <span className="text-xl font-bold text-blue-600">
                          {formatCurrency(parseFloat(offer.pricing.total_amount), offer.pricing.total_currency)}
                        </span>
                        <span className="text-xs text-gray-500">{t('results.totalFor', { count: passengerCount })}</span>
                        {passengerCount > 1 && (
                          <span className="text-sm text-gray-700 mt-1">
                            {formatCurrency(parseFloat(offer.pricing.total_amount) / passengerCount, offer.pricing.total_currency)}
                            <span className="ml-1 text-xs text-gray-500">{t('results.perPerson')}</span>
                          </span>
                        )}
                      </div>
                      <div className="flex gap-2 justify-end">
                        <button
                          onClick={() => {
                            setSelectedOffer(offer);
                            setShowModal(true);
                          }}
                          className="bg-gray-100 hover:bg-gray-200 text-gray-700 px-3 py-1 rounded-lg text-xs font-medium transition-colors flex items-center gap-1"
                        >
                          <Info className="h-3 w-3" />
                          Details
                        </button>
                        <button
                          onClick={() => onSelectFlight(offer)}
                          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors flex items-center gap-1"
                        >
                          <Zap className="h-3 w-3" />
                          Select
                        </button>
                      </div>
                    </div>
                  </div>

                  {/* Additional Info - Only for connecting flights */}
                  {slice.segments.length > 1 && (
                    <div className="mt-3 pt-3 border-t border-gray-100">
                      <div className="flex items-center gap-4 text-xs text-gray-500">
                        <span>Stops: {slice.segments.map((s: any) => s.destination.iata_code).slice(0, -1).join(', ')}</span>
                        <span>•</span>
                        <span>Aircraft: {slice.segments[0]?.aircraft?.name || 'Various'}</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        ))}
      </div>

      {/* Flight Details Modal */}
      {showModal && selectedOffer && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4 z-50">
          <div className="bg-white rounded-2xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            {/* Modal Header */}
            <div className="flex items-center justify-between p-6 border-b border-gray-200">
              <h3 className="text-xl font-bold text-gray-900 flex items-center gap-2">
                <Plane className="h-5 w-5 text-blue-600" />
                Flight Details
              </h3>
              <button
                onClick={() => setShowModal(false)}
                className="text-gray-400 hover:text-gray-600 transition-colors"
              >
                <X className="h-6 w-6" />
              </button>
            </div>

            {/* Modal Content */}
            <div className="p-6">
              {/* Airline Info */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 mb-6">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center text-white font-bold text-lg overflow-hidden">
                    {selectedOffer.flight_details.slices[0]?.segments[0]?.marketing_carrier.logo_symbol_url ? (
                      <img
                        src={selectedOffer.flight_details.slices[0].segments[0].marketing_carrier.logo_symbol_url}
                        alt={selectedOffer.airline.name}
                        className="w-full h-full object-contain"
                      />
                    ) : (
                      selectedOffer.airline.code
                    )}
                  </div>
                  <div>
                    <h4 className="text-lg font-bold text-gray-900">{selectedOffer.airline.name}</h4>
                    <p className="text-gray-600">
                      Flight {selectedOffer.flight_details.slices[0]?.segments[0]?.marketing_carrier.iata_code}
                      {selectedOffer.flight_details.slices[0]?.segments[0]?.marketing_carrier_flight_number}
                    </p>
                  </div>
                </div>
              </div>

              {/* Flight Segments */}
              {selectedOffer.flight_details.slices.map((slice, sliceIndex) => (
                <div key={sliceIndex} className="mb-6">
                  <h5 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-blue-600" />
                    {slice.origin.city_name} → {slice.destination.city_name}
                  </h5>

                  {slice.segments.map((segment, segmentIndex) => (
                    <div key={segmentIndex} className="bg-white border border-gray-200 rounded-xl p-4 mb-4">
                      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                        {/* Departure */}
                        <div>
                          <h6 className="text-sm font-medium text-gray-500 mb-1">Departure</h6>
                          <div className="text-xl font-bold text-gray-900">
                            {formatTime(new Date(segment.departing_at))}
                          </div>
                          <div className="text-sm font-semibold text-blue-600">
                            {segment.origin.iata_code} - {segment.origin.name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {new Date(segment.departing_at).toLocaleDateString()}
                          </div>
                        </div>

                        {/* Flight Info */}
                        <div className="text-center">
                          <h6 className="text-sm font-medium text-gray-500 mb-1">Flight</h6>
                          <div className="text-sm font-semibold text-gray-900">
                            {segment.marketing_carrier.name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {segment.marketing_carrier.iata_code}{segment.marketing_carrier_flight_number}
                          </div>
                          <div className="text-xs text-gray-500">
                            {segment.aircraft?.name || 'Aircraft TBD'}
                          </div>
                          <div className="text-xs text-gray-500 mt-1">
                            Duration: {formatDuration(segment.duration)}
                          </div>
                        </div>

                        {/* Arrival */}
                        <div className="text-right">
                          <h6 className="text-sm font-medium text-gray-500 mb-1">Arrival</h6>
                          <div className="text-xl font-bold text-gray-900">
                            {formatTime(new Date(segment.arriving_at))}
                          </div>
                          <div className="text-sm font-semibold text-blue-600">
                            {segment.destination.iata_code} - {segment.destination.name}
                          </div>
                          <div className="text-xs text-gray-500">
                            {new Date(segment.arriving_at).toLocaleDateString()}
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ))}

              {/* Price Breakdown */}
              <div className="bg-gradient-to-r from-blue-50 to-indigo-50 rounded-xl p-4 border border-blue-100">
                <h5 className="text-lg font-semibold text-gray-900 mb-4 flex items-center gap-2">
                  <Star className="h-4 w-4 text-blue-600" />
                  Price Breakdown
                </h5>
                <div className="space-y-2">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Base fare:</span>
                    <span className="font-medium">{formatCurrency(parseFloat(selectedOffer.pricing.base_amount), selectedOffer.pricing.base_currency)}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Taxes & fees:</span>
                    <span className="font-medium">{formatCurrency(parseFloat(selectedOffer.pricing.tax_amount), selectedOffer.pricing.tax_currency)}</span>
                  </div>
                  <div className="border-t border-blue-200 pt-2 mt-2">
                    <div className="flex justify-between">
                      <span className="text-lg font-bold text-gray-900">Total:</span>
                      <span className="text-lg font-bold text-blue-600">
                        {formatCurrency(parseFloat(selectedOffer.pricing.total_amount), selectedOffer.pricing.total_currency)}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Modal Footer */}
            <div className="flex items-center justify-between p-6 border-t border-gray-200">
              <div className="flex items-center gap-2 text-sm text-gray-500">
                <Shield className="h-4 w-4" />
                <span>Price locked for 10 minutes</span>
              </div>
              <div className="flex gap-3">
                <button
                  onClick={() => setShowModal(false)}
                  className="px-4 py-2 text-gray-700 bg-gray-100 hover:bg-gray-200 rounded-lg font-medium transition-colors"
                >
                  Close
                </button>
                <button
                  onClick={() => {
                    setShowModal(false);
                    onSelectFlight(selectedOffer);
                  }}
                  className="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-lg font-medium transition-colors flex items-center gap-2"
                >
                  <Zap className="h-4 w-4" />
                  Select Flight
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
